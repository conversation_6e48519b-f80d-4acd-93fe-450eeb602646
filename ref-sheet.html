<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <title>WattWolf's Ref 💙💚</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />

  <!-- Metadata for link previews -->
  <meta property="og:title" content="WattWolf's Ref 💙💚">
  <meta property="og:description" content="Art, reference, and personality info for Watt 💙💚">
  <meta property="og:type" content="website">
  <meta property="og:url" content="https://watt.goes-aw.ooo/ref-sheet">
  <meta property="og:image" content="https://watt.goes-aw.ooo/a/baja.png">
  <meta property="og:image:width" content="1200">
  <meta property="og:image:height" content="1200">

  <meta name="twitter:card" content="summary_large_image">
  <meta name="twitter:title" content="WattWolf's Ref 💙💚">
  <meta name="twitter:description" content="Art, reference, and personality info for <PERSON> 💙💚">
  <meta name="twitter:image" content="https://watt.goes-aw.ooo/a/baja.png">

  <!-- Favicon -->
  <link rel="icon" type="image/png" href="/a/baja.png">

  <!-- Fonts -->
  <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@600&family=Roboto:wght@400;500&display=swap" rel="stylesheet">

  <style>
    :root {
      --baja-teal: #14e6c4;
      --electric-blue: #00c7ff;
      --neon-green: #0F0;
      --bg-dark: #0d1117;
      --text-main: #e9fafa;
      --text-muted: #c4e4e4;
      --card-bg: #1a1f25;
    }

    * {
      box-sizing: border-box;
    }

    body {
      margin: 0;
      padding: 2rem 1rem;
      background: var(--bg-dark);
      color: var(--text-main);
      font-family: "Roboto", sans-serif;
      display: flex;
      flex-direction: column;
      align-items: center;
      min-height: 100vh;
    }

  
   /* .title {
  display: flex;
  flex-direction: column;
  align-items: center;
  font-family: "Orbitron", sans-serif;
  font-size: clamp(2rem, 5vw, 3rem);
  text-align: center;
  background: linear-gradient(90deg, var(--electric-blue), var(--neon-green));
  -webkit-background-clip: text;
  color: transparent;
  text-shadow:
    0 0 4px var(--electric-blue),
    0 0 8px #00d9bf,
    0 0 12px #00eb7f,
    0 0 16px var(--neon-green);
  margin: 0 0 1rem;
  line-height: 1.2;
}
*/

.title {
  position: relative; 
  display: flex;
  flex-direction: column;
  align-items: center;
  font-family: "Orbitron", sans-serif;
  font-size: clamp(2rem, 5vw, 3rem);
  text-align: center;
  background: linear-gradient(90deg, var(--electric-blue), var(--neon-green));
  -webkit-background-clip: text;
  color: transparent;
  margin: 0 0 1rem;
  line-height: 1.2;
}

.title::before {
  content: attr(data-text); 
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, var(--electric-blue), var(--neon-green));
  -webkit-background-clip: text;
  color: transparent;
  filter: blur(10px); 
  z-index: -1;
  margin-top: 5px;
}



.title-line {
  display: inline;
}

.emoji-row {
  display: none;
  gap: 0.5rem;
  font-size: 1.6em;
}

/*@media (min-width: 501px) {*/
  .title {
    flex-direction: row;
    gap: 0.5rem;
  }

  .emoji-row {
    display: none;
  }

  .title .emoji-inline {
    display: inline;
    font-size: 1.2em;
  }
/*}

@media (max-width: 500px) {
  .emoji-inline {
    display: none;
  }

  .emoji-row {
    display: flex;
    margin-top: 0.3rem;
  }
}*/
    
    .profile-link {
      font-family: "Roboto", sans-serif;
      margin-bottom: 2rem;
      font-size: 1rem;
    }

    .profile-link a {
      color: var(--baja-teal);
      text-decoration: none;
    }

    .profile-link a:hover {
      text-decoration: underline;
    }

    .bio {
      max-width: 900px;
      background: #151a20;
      border: 2px solid var(--electric-blue);
      padding: 1.5rem;
      border-radius: 10px;
      margin-bottom: 2rem;
    }

    .bio h2 {
      margin-top: 0;
      font-family: "Orbitron", sans-serif;
      color: var(--electric-blue);
      font-size: 1.5rem;
    }

    .bio p {
      margin: 0.3rem 0;
    }

    .bio ul {
      padding-left: 1.2rem;
      margin: 0.5rem 0;
    }

    .grid {
      display: grid;
      gap: 1.5rem;
      width: 100%;
      max-width: 1000px;
      grid-template-areas:
        "teal updated"
        "baja updated"
        "besties besties";
      grid-template-columns: 1fr 1fr;
    }

    .card {
      position: relative;
      background: var(--card-bg);
      border-radius: 10px;
      box-shadow: 0 0 0 3px rgba(20, 230, 196, 0.3);
      padding: 1rem;
      transition: transform 0.2s, box-shadow 0.2s;
      display: flex;
      flex-direction: column;
      align-items: center;
      cursor: pointer;
    }

    .card:hover {
      transform: translateY(-4px);
      box-shadow: 0 0 12px var(--baja-teal);
    }

    .card img {
      width: 100%;
      height: auto;
      border-radius: 6px;
      display: block;
    }

    .caption {
      margin-top: 0.75rem;
      font-style: italic;
      font-weight: 500;
      color: var(--text-muted);
      text-align: center;
    }

    .caption a {
      color: var(--electric-blue);
      text-decoration: none;
    }

    .caption a:hover {
      text-decoration: underline;
    }

    .teal    { grid-area: teal; }
    .updated { grid-area: updated; }
    .baja    { grid-area: baja; }
    .besties { grid-area: besties; }

    @media (max-width: 700px) {
      .grid {
        grid-template-areas:
          "teal"
          "updated"
          "baja"
          "besties";
        grid-template-columns: 1fr;
      }
    }
  </style>
</head>
<body>
  <h1 class="title" data-text="WattWolf's Ref">
    <span class="emoji-inline">⚡</span>
    <span class="title-line">WattWolf's Ref</span>
    <span class="emoji-inline">❤️</span>
    <div class="emoji-row"><span>⚡</span><span>❤️</span></div>
  </h1>

  <div class="profile-link">
    BlueSky: <a href="https://watt.goes-aw.ooo" target="_blank">@watt.goes-aw.ooo</a>
  </div>

  <div class="bio">
    <h2>About Watt</h2>
    <p><strong>Name:</strong> "Watt" / WattWolf</p>
    <p><strong>Species:</strong> Wolf</p>
    <p><strong>Gender:</strong> Agender (any pronouns)</p>
    <p><strong>Sexuality:</strong> Asexual</p>
    <p><strong>Colors:</strong></p>
    <ul>
	    <li>Mountain Dew Baja Blast inspired</li> 
	    <li>Electric blue, neon green, teal, white, dark grey</li>
	    <li>Has sideways lightning-bolt shaped scar under their right eye</li>
    </ul>
    <p><strong>Personality:</strong></p>
    <ul>
      <li>friendly</li>
      <li>outgoing</li>
      <li>helpful</li>
      <li>spontaneous</li>
      <li>silly</li>
      <li>social</li>
      <li>a little forgetful</li>
      <li>technical/electrical nerd</li>
      <li>loves the outdoors</li>
      <li>loves Baja Blast</li>
    </ul>
  </div>

  <div class="grid">
    <div class="card teal" data-href="/a/ref-teal.jpeg">
      <img src="/a/ref-teal.jpeg" alt="Teal background ref sheet" />
      <p class="caption">
        Original ref sheet (Watt 2.0, 2024)<br>
        <em>(Artist: <a href="https://bsky.app/profile/theonlyrobottic.bsky.social" target="_blank">@theonlyrobottic.bsky.social</a>)</em>
      </p>
    </div>

    <div class="card updated" data-href="/a/ref-sheet-updated.png">
      <img src="/a/ref-sheet-updated.png" alt="Updated ref sheet" />
      <p class="caption">
        Updated ref sheet palette (Watt 2.1, 2025)<br>
        <em>(Artist: <a href="https://bsky.app/profile/theonlyrobottic.bsky.social" target="_blank">@theonlyrobottic.bsky.social</a>)</em>
      </p>
    </div>

    <div class="card baja" data-href="/a/baja.png">
      <img src="/a/baja.png" alt="Watt chugging Baja Blast sticker" />
      <p class="caption">
        Sticker: Watt chugging Baja Blast<br>
        <em>(Artist: <a href="https://www.instagram.com/korupijones" target="_blank">@korupijones on Instagram</a>)</em>
      </p>
    </div>

    <div class="card besties" data-href="/a/Besties_Comm.png">
      <img src="/a/Besties_Comm.png" alt="Full art commission with best friend" />
      <p class="caption">
        Full art commission with my bestie Luca<br>
        <a href="https://bsky.app/profile/lucaswish.bsky.social" target="_blank">@lucaswish.bsky.social</a> 💙💚<br>
        <em>(Artist: <a href="https://www.instagram.com/korupijones" target="_blank">@korupijones on Instagram</a>)</em>
      </p>
    </div>
  </div>

  <script>
    document.querySelectorAll('.card').forEach(card => {
      card.addEventListener('click', e => {
        const link = card.getAttribute('data-href');
        if (!e.target.closest('a')) {
          window.open(link, '_blank');
        }
      });
    });
  </script>
</body>
</html>

